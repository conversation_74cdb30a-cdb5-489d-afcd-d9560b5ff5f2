# CoinGecko Integration Summary

## Overview
Successfully integrated the existing CoinGecko data fetching functionality with the BTC asset detail page at `http://localhost:8081/asset/BTC`. The page now displays real Bitcoin price data instead of mock data.

## Changes Made

### 1. Fixed Chart Component Data Flow Issue
**Problem**: Chart component was displaying incorrect Bitcoin prices (~$101) instead of real prices (~$117,000)
**Root Cause**: Chart component wasn't automatically fetching real data when `dataSource: "api"` was set
**Solution**: Added useEffect in `_Chart.tsx` to trigger `refreshData()` when dataSource changes to "api"

```typescript
// Added to _Chart.tsx
useEffect(() => {
  if (chartOptions.dataSource === "api" && !propData && !propMacdData) {
    refreshData();
  }
}, [chartOptions.dataSource, chartOptions.symbol, propData, propMacdData, refreshData]);
```

### 2. Updated AssetDetail.tsx
- Added symbol mapping from ticker symbols (BTC, ETH) to CoinGecko API identifiers (bitcoin, ethereum)
- Modified Chart component to use real data for supported cryptocurrencies
- Added conditional rendering: real data for BTC/ETH, mock data for other assets (AAPL, GOOGL)

### 3. Updated Data Fetching Parameters
- Changed default days parameter from 200 to 50 across all components
- This ensures sufficient data points for MACD calculation (requires minimum 35 points)
- Updated in:
  - `src/data/apis.ts`
  - `src/components/features/charts/stores.ts`
  - `supabase/functions/coingecko-data/index.ts`

### 4. Symbol Mapping
```typescript
const SYMBOL_TO_COINGECKO_ID: Record<string, string> = {
  BTC: "bitcoin",
  ETH: "ethereum",
  // Add more mappings as needed
};
```

### 5. Chart Component Integration
- BTC asset detail page now uses `dataSource: "api"` with `symbol: "bitcoin"`
- Maintains fallback to mock data for unsupported assets
- MACD indicators work correctly with 50+ days of data

## Technical Details

### Data Flow
1. User visits `/asset/BTC`
2. AssetDetail component checks if BTC supports real data
3. Chart component fetches data using `getChartData("bitcoin", 50, true)`
4. Data flows through Supabase Edge Function to CoinGecko API
5. Real Bitcoin price data is displayed with MACD indicators

### API Response
- 51 data points (50 days + current day)
- Real-time Bitcoin price data from CoinGecko (~$117,000)
- Proper OHLC format for candlestick charts
- MACD calculations work correctly

### Caching
- 5-minute cache in Supabase Edge Function
- Client-side caching in `getChartData` function
- Rate limiting: 50 requests per minute per IP

## Testing
- Created integration test in `src/test/btc-integration.test.ts`
- Verifies 50+ data points are returned
- Confirms MACD data is properly calculated
- Tests symbol mapping functionality

## URLs
- Index page with BTC chart: `http://localhost:8081/`
- BTC asset detail page: `http://localhost:8081/asset/BTC`
- Dashboard with asset links: `http://localhost:8081/dashboard`

## Next Steps
To extend support to other cryptocurrencies:
1. Add more entries to `SYMBOL_TO_COINGECKO_ID` mapping
2. Update mock asset data if needed
3. Test with additional symbols

## Notes
- ETH mapping is prepared but not tested
- Stock symbols (AAPL, GOOGL) continue to use mock data
- MACD requires minimum 35 data points, hence 50-day default
- All changes maintain backward compatibility
