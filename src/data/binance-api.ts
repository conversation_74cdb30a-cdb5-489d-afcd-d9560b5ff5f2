import { CandlestickData } from "lightweight-charts";
import { UTCTimestamp } from "lightweight-charts";

// Binance API Response Types
export interface BinanceKlineResponse {
  // [timestamp, open, high, low, close, volume, closeTime, quoteVolume, count, takerBuyBaseVolume, takerBuyQuoteVolume, ignore]
  data: [number, string, string, string, string, string, number, string, number, string, string, string][];
}

export interface BinanceChartDataResponse {
  data: CandlestickData[];
  source: "binance";
  symbol: string;
  interval: string;
  lastUpdate: string;
}

// Symbol mapping for Binance
const BINANCE_SYMBOL_MAP: Record<string, string> = {
  bitcoin: "BTCUSDT",
  ethereum: "ETHUSDT",
  cardano: "ADAUSDT",
  polkadot: "DOTUSDT",
  chainlink: "LINKUSDT",
  litecoin: "LTCUSDT",
  "bitcoin-cash": "BCHUSDT",
  stellar: "XLMUSDT",
  "usd-coin": "USDCUSDT",
  dogecoin: "DOGEUSDT",
};

// Interval mapping
const BINANCE_INTERVALS = {
  "1m": "1m",
  "5m": "5m", 
  "15m": "15m",
  "30m": "30m",
  "1h": "1h",
  "4h": "4h",
  "1d": "1d",
  "1w": "1w",
  daily: "1d",
  hourly: "1h",
} as const;

export class BinanceApiError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message);
    this.name = "BinanceApiError";
  }
}

// Transform Binance kline data to chart format
export const transformBinanceData = (klineData: any[]): CandlestickData[] => {
  if (!Array.isArray(klineData) || klineData.length === 0) {
    return [];
  }

  return klineData.map((kline, index) => {
    if (!Array.isArray(kline) || kline.length < 6) {
      throw new Error(`Invalid Binance kline data at index ${index}`);
    }

    const [timestamp, open, high, low, close] = kline;
    const time = Math.floor(timestamp / 1000) as UTCTimestamp;

    return {
      time,
      open: Number(parseFloat(open).toFixed(2)),
      high: Number(parseFloat(high).toFixed(2)),
      low: Number(parseFloat(low).toFixed(2)),
      close: Number(parseFloat(close).toFixed(2)),
    };
  });
};

// Fetch OHLC data from Binance API
export const fetchBinanceOHLC = async (
  symbol: string = "bitcoin",
  days: number = 50,
  interval: string = "daily"
): Promise<BinanceChartDataResponse> => {
  try {
    // Map symbol to Binance format
    const binanceSymbol = BINANCE_SYMBOL_MAP[symbol.toLowerCase()];
    if (!binanceSymbol) {
      throw new BinanceApiError(`Unsupported symbol: ${symbol}. Supported symbols: ${Object.keys(BINANCE_SYMBOL_MAP).join(", ")}`);
    }

    // Map interval to Binance format
    const binanceInterval = BINANCE_INTERVALS[interval as keyof typeof BINANCE_INTERVALS] || "1d";
    
    // Calculate limit based on days and interval
    let limit = days;
    if (binanceInterval === "1h") {
      limit = days * 24; // 24 hours per day
    } else if (binanceInterval === "4h") {
      limit = days * 6; // 6 periods per day
    }
    
    // Binance allows max 1000 data points
    limit = Math.min(limit, 1000);

    const url = `https://api.binance.com/api/v3/klines?symbol=${binanceSymbol}&interval=${binanceInterval}&limit=${limit}`;
    
    console.log(`Fetching Binance OHLC data: ${url}`);

    const response = await fetch(url, {
      headers: {
        "Accept": "application/json",
        "User-Agent": "Signal-Sight-Portfolio-Tracker/1.0",
      },
    });

    if (!response.ok) {
      throw new BinanceApiError(
        `Binance API error: ${response.status} ${response.statusText}`,
        response.status
      );
    }

    const klineData = await response.json();

    // Validate response structure
    if (!Array.isArray(klineData)) {
      throw new BinanceApiError("Invalid Binance API response format");
    }

    // Transform to chart format
    const chartData = transformBinanceData(klineData);

    console.log(`Successfully fetched ${chartData.length} OHLC data points from Binance`);

    return {
      data: chartData,
      source: "binance",
      symbol: binanceSymbol,
      interval: binanceInterval,
      lastUpdate: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error fetching Binance OHLC data:", error);
    
    if (error instanceof BinanceApiError) {
      throw error;
    }
    
    throw new BinanceApiError(
      `Failed to fetch Binance data: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }
};

// Get supported symbols
export const getBinanceSupportedSymbols = (): string[] => {
  return Object.keys(BINANCE_SYMBOL_MAP);
};

// Check if symbol is supported
export const isBinanceSymbolSupported = (symbol: string): boolean => {
  return symbol.toLowerCase() in BINANCE_SYMBOL_MAP;
};

// Get Binance symbol for a given coin
export const getBinanceSymbol = (symbol: string): string | null => {
  return BINANCE_SYMBOL_MAP[symbol.toLowerCase()] || null;
};
