import { CandlestickData, HistogramData } from "lightweight-charts";
import {
  transformCoinGeckoData,
  calculateMACDHistogram,
  generateFakeData,
  generateFakeMACDData,
} from "@/components/features/charts/utils";

// API Response interfaces
export interface CoinGeckoResponse {
  prices: number[][];
  market_caps: number[][];
  total_volumes: number[][];
}

export class ApiError extends Error {
  status?: number;
  code?: string;

  constructor(options: { message: string; status?: number; code?: string }) {
    super(options.message);
    this.name = "ApiError";
    this.status = options.status;
    this.code = options.code;
  }
}

export interface ChartDataResponse {
  data: CandlestickData[];
  macdData: HistogramData[];
  source: "api" | "mock";
  lastUpdated: Date;
  symbol: string;
}

// Configuration
const SUPABASE_FUNCTION_URL = import.meta.env.VITE_SUPABASE_URL
  ? `${import.meta.env.VITE_SUPABASE_URL}/functions/v1`
  : "http://127.0.0.1:54321/functions/v1";

const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || "";

// Fetch data from Supabase Edge Function
export const fetchCoinGeckoData = async (
  symbol: string = "bitcoin",
  days: number = 50,
  interval: string = "daily"
): Promise<ChartDataResponse> => {
  try {
    const response = await fetch(`${SUPABASE_FUNCTION_URL}/coingecko-data`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${SUPABASE_ANON_KEY}`,
      },
      body: JSON.stringify({
        symbol,
        days,
        interval,
      }),
    });

    if (!response.ok) {
      throw new ApiError({
        message: `API request failed: ${response.statusText}`,
        status: response.status,
      });
    }

    const coinGeckoData: CoinGeckoResponse = await response.json();

    if (!coinGeckoData.prices || !Array.isArray(coinGeckoData.prices)) {
      throw new ApiError({
        message: "Invalid API response format",
        code: "INVALID_RESPONSE",
      });
    }

    // Transform CoinGecko data to chart format
    const chartData = transformCoinGeckoData(coinGeckoData.prices);

    // Calculate MACD from price data
    const macdData = calculateMACDHistogram(chartData);

    return {
      data: chartData,
      macdData,
      source: "api",
      lastUpdated: new Date(),
      symbol: symbol.toUpperCase(),
    };
  } catch (error) {
    console.error("Failed to fetch CoinGecko data:", error);

    // Return mock data as fallback
    return getFallbackData(symbol);
  }
};

// Fallback to mock data
export const getFallbackData = (symbol: string = "BTC"): ChartDataResponse => {
  console.log("Using fallback mock data for", symbol);

  const mockData = generateFakeData({
    dataPoints: 50,
    basePrice: symbol === "bitcoin" || symbol === "BTC" ? 45000 : 100,
    startDate: new Date(Date.now() - 50 * 24 * 60 * 60 * 1000), // 50 days ago
  });

  const mockMacdData = generateFakeMACDData({
    dataPoints: 50,
    startDate: new Date(Date.now() - 50 * 24 * 60 * 60 * 1000),
  });

  return {
    data: mockData,
    macdData: mockMacdData,
    source: "mock",
    lastUpdated: new Date(),
    symbol: symbol.toUpperCase(),
  };
};

// Generic API error handler
export const handleApiError = (error: unknown): ApiError => {
  if (error instanceof ApiError) {
    return error;
  }

  if (error instanceof Error) {
    return new ApiError({
      message: error.message,
      code: "UNKNOWN_ERROR",
    });
  }

  return new ApiError({
    message: "An unknown error occurred",
    code: "UNKNOWN_ERROR",
  });
};

// Retry mechanism for API calls
export const retryApiCall = async <T>(
  apiCall: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: unknown;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error;

      if (attempt === maxRetries) {
        break;
      }

      // Wait before retrying
      await new Promise((resolve) => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError;
};

// Main function to get chart data with retry and fallback
export const getChartData = async (
  symbol: string = "bitcoin",
  days: number = 50,
  useCache: boolean = true
): Promise<ChartDataResponse> => {
  // Check cache first (if implemented)
  if (useCache) {
    const cachedData = getCachedData(symbol);
    if (cachedData) {
      return cachedData;
    }
  }

  try {
    // Try to fetch from API with retry
    const data = await retryApiCall(() => fetchCoinGeckoData(symbol, days), 3, 1000);

    // Cache the successful response
    if (useCache) {
      setCachedData(symbol, data);
    }

    return data;
  } catch (error) {
    console.error("All API attempts failed, using fallback data:", error);
    return getFallbackData(symbol);
  }
};

// Simple in-memory cache (in production, use localStorage or IndexedDB)
const cache = new Map<string, { data: ChartDataResponse; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

const getCachedData = (symbol: string): ChartDataResponse | null => {
  const cached = cache.get(symbol);

  if (!cached) {
    return null;
  }

  const isExpired = Date.now() - cached.timestamp > CACHE_DURATION;

  if (isExpired) {
    cache.delete(symbol);
    return null;
  }

  return cached.data;
};

const setCachedData = (symbol: string, data: ChartDataResponse): void => {
  cache.set(symbol, {
    data,
    timestamp: Date.now(),
  });
};

// Clear cache
export const clearCache = (): void => {
  cache.clear();
};

// Get cache status
export const getCacheStatus = () => {
  return {
    size: cache.size,
    entries: Array.from(cache.keys()),
  };
};
