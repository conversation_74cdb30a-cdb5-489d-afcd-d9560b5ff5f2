import { CandlestickData } from "lightweight-charts";
import { UTCTimestamp } from "lightweight-charts";

// Vietnamese Stock API Response Types
export interface TradingViewResponse {
  totalCount: number;
  data: Array<{
    s: string; // Symbol (e.g., "HOSE:VIC")
    d: [string, number, number, number, number, number, number, number]; // [name, close, open, high, low, volume, change%, change_abs]
  }>;
}

export interface TCBSCompanyResponse {
  exchange: string;
  shortName: string;
  industryID: number;
  industry: string;
  industryEn: string;
  establishedYear: string;
  noEmployees: number;
  noShareholders: number;
  foreignPercent: number;
  website: string;
  stockRating: number;
  deltaInWeek: number;
  deltaInMonth: number;
  deltaInYear: number;
  outstandingShare: number;
  issueShare: number;
  ticker: string;
}

export interface VietnameseStockDataResponse {
  data: CandlestickData[];
  source: "tradingview" | "tcbs" | "vnstock";
  symbol: string;
  exchange: "HOSE" | "HNX" | "UPCOM";
  lastUpdate: string;
  companyInfo?: TCBSCompanyResponse;
}

// Vietnamese stock symbol mapping
const VIETNAMESE_STOCK_SYMBOLS = {
  // Major Vietnamese stocks
  "VIC": { name: "VinGroup", exchange: "HOSE" },
  "VCB": { name: "Vietcombank", exchange: "HOSE" },
  "FPT": { name: "FPT Corporation", exchange: "HOSE" },
  "VNM": { name: "Vinamilk", exchange: "HOSE" },
  "HPG": { name: "Hoa Phat Group", exchange: "HOSE" },
  "TCB": { name: "Techcombank", exchange: "HOSE" },
  "BID": { name: "BIDV", exchange: "HOSE" },
  "CTG": { name: "VietinBank", exchange: "HOSE" },
  "VHM": { name: "Vinhomes", exchange: "HOSE" },
  "ACB": { name: "Asia Commercial Bank", exchange: "HOSE" },
  "MBB": { name: "Military Bank", exchange: "HOSE" },
  "VPB": { name: "VPBank", exchange: "HOSE" },
  "GAS": { name: "PetroVietnam Gas", exchange: "HOSE" },
  "MSN": { name: "Masan Group", exchange: "HOSE" },
  "MWG": { name: "Mobile World", exchange: "HOSE" },
  "VRE": { name: "Vincom Retail", exchange: "HOSE" },
  "SSI": { name: "SSI Securities", exchange: "HOSE" },
  "VJC": { name: "VietJet Air", exchange: "HOSE" },
  "REE": { name: "Refrigeration Electrical Engineering", exchange: "HOSE" },
  "PNJ": { name: "Phu Nhuan Jewelry", exchange: "HOSE" },
} as const;

export class VietnameseStockApiError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message);
    this.name = "VietnameseStockApiError";
  }
}

// Fetch current OHLC data from TradingView
export const fetchTradingViewData = async (
  symbol: string,
  exchange: string = "HOSE"
): Promise<CandlestickData | null> => {
  try {
    const tradingViewSymbol = `${exchange}:${symbol.toUpperCase()}`;
    
    const requestBody = {
      filter: [
        {
          left: "name",
          operation: "match",
          right: symbol.toUpperCase()
        }
      ],
      options: { lang: "en" },
      markets: ["vietnam"],
      symbols: { query: { types: [] }, tickers: [] },
      columns: ["name", "close", "open", "high", "low", "volume", "change", "change_abs"],
      sort: { sortBy: "market_cap_basic", sortOrder: "desc" },
      range: [0, 1]
    };

    const response = await fetch("https://scanner.tradingview.com/vietnam/scan", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "User-Agent": "Signal-Sight-Portfolio-Tracker/1.0",
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new VietnameseStockApiError(
        `TradingView API error: ${response.status} ${response.statusText}`,
        response.status
      );
    }

    const data: TradingViewResponse = await response.json();

    if (!data.data || data.data.length === 0) {
      return null;
    }

    const stockData = data.data[0];
    const [name, close, open, high, low, volume] = stockData.d;

    // Create current day candlestick data
    const now = new Date();
    const time = Math.floor(now.getTime() / 1000) as UTCTimestamp;

    return {
      time,
      open: Number(open.toFixed(0)),
      high: Number(high.toFixed(0)),
      low: Number(low.toFixed(0)),
      close: Number(close.toFixed(0)),
    };
  } catch (error) {
    console.error("Error fetching TradingView data:", error);
    
    if (error instanceof VietnameseStockApiError) {
      throw error;
    }
    
    throw new VietnameseStockApiError(
      `Failed to fetch TradingView data: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }
};

// Fetch company information from TCBS API
export const fetchTCBSCompanyInfo = async (
  symbol: string
): Promise<TCBSCompanyResponse | null> => {
  try {
    const url = `https://apipubaws.tcbs.com.vn/tcanalysis/v1/ticker/${symbol.toUpperCase()}/overview`;
    
    const response = await fetch(url, {
      headers: {
        "Accept": "application/json",
        "User-Agent": "Signal-Sight-Portfolio-Tracker/1.0",
      },
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null; // Symbol not found
      }
      throw new VietnameseStockApiError(
        `TCBS API error: ${response.status} ${response.statusText}`,
        response.status
      );
    }

    const data: TCBSCompanyResponse = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching TCBS company info:", error);
    
    if (error instanceof VietnameseStockApiError) {
      throw error;
    }
    
    return null; // Return null for non-critical errors
  }
};

// Generate historical OHLC data for Vietnamese stocks
// Note: This creates simulated historical data based on current price
// In a real implementation, you would fetch actual historical data
export const generateVietnameseHistoricalData = (
  currentData: CandlestickData,
  days: number = 20
): CandlestickData[] => {
  const historicalData: CandlestickData[] = [];
  const currentPrice = currentData.close;
  
  // Generate historical data working backwards from current date
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    date.setHours(0, 0, 0, 0);
    
    // Skip weekends (Vietnamese stock market is closed)
    if (date.getDay() === 0 || date.getDay() === 6) {
      continue;
    }
    
    const time = Math.floor(date.getTime() / 1000) as UTCTimestamp;
    
    // Simulate price movement with Vietnamese market characteristics
    // Vietnamese stocks tend to have higher volatility than developed markets
    const volatility = 0.03 + Math.random() * 0.04; // 3-7% daily volatility
    const trend = Math.sin(i / 5) * 0.01; // Slight trend component
    const randomWalk = (Math.random() - 0.5) * volatility;
    
    const priceMultiplier = 1 + trend + randomWalk;
    const basePrice = currentPrice * Math.pow(priceMultiplier, i / days);
    
    // Generate OHLC with realistic Vietnamese market patterns
    const openOffset = (Math.random() - 0.5) * volatility * 0.3;
    const highOffset = Math.random() * volatility * 0.8;
    const lowOffset = -Math.random() * volatility * 0.8;
    
    const open = Math.round(basePrice * (1 + openOffset));
    const high = Math.round(Math.max(open, basePrice) * (1 + highOffset));
    const low = Math.round(Math.min(open, basePrice) * (1 + lowOffset));
    const close = i === 0 ? currentData.close : Math.round(basePrice);
    
    historicalData.push({
      time,
      open,
      high: Math.max(high, open, close),
      low: Math.min(low, open, close),
      close,
    });
  }
  
  return historicalData.sort((a, b) => a.time - b.time);
};

// Main function to get Vietnamese stock data
export const getVietnameseStockData = async (
  symbol: string = "VIC",
  days: number = 20
): Promise<VietnameseStockDataResponse> => {
  try {
    // Validate symbol
    const symbolInfo = VIETNAMESE_STOCK_SYMBOLS[symbol.toUpperCase() as keyof typeof VIETNAMESE_STOCK_SYMBOLS];
    if (!symbolInfo) {
      throw new VietnameseStockApiError(`Unsupported Vietnamese stock symbol: ${symbol}. Supported symbols: ${Object.keys(VIETNAMESE_STOCK_SYMBOLS).join(", ")}`);
    }

    console.log(`Fetching Vietnamese stock data for ${symbol.toUpperCase()} (${symbolInfo.name})`);

    // Fetch current OHLC data from TradingView
    const currentData = await fetchTradingViewData(symbol, symbolInfo.exchange);
    
    if (!currentData) {
      throw new VietnameseStockApiError(`No current data available for ${symbol}`);
    }

    // Generate historical data
    const historicalData = generateVietnameseHistoricalData(currentData, days);

    // Fetch company information from TCBS
    const companyInfo = await fetchTCBSCompanyInfo(symbol);

    console.log(`Successfully fetched ${historicalData.length} data points for ${symbol.toUpperCase()}`);

    return {
      data: historicalData,
      source: "tradingview",
      symbol: symbol.toUpperCase(),
      exchange: symbolInfo.exchange,
      lastUpdate: new Date().toISOString(),
      companyInfo: companyInfo || undefined,
    };
  } catch (error) {
    console.error("Error fetching Vietnamese stock data:", error);
    
    if (error instanceof VietnameseStockApiError) {
      throw error;
    }
    
    throw new VietnameseStockApiError(
      `Failed to fetch Vietnamese stock data: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }
};

// Get supported Vietnamese stock symbols
export const getSupportedVietnameseSymbols = (): string[] => {
  return Object.keys(VIETNAMESE_STOCK_SYMBOLS);
};

// Check if symbol is supported
export const isVietnameseSymbolSupported = (symbol: string): boolean => {
  return symbol.toUpperCase() in VIETNAMESE_STOCK_SYMBOLS;
};

// Get Vietnamese stock info
export const getVietnameseStockInfo = (symbol: string) => {
  return VIETNAMESE_STOCK_SYMBOLS[symbol.toUpperCase() as keyof typeof VIETNAMESE_STOCK_SYMBOLS] || null;
};
