import { describe, it, expect } from 'vitest';
import { getChartData } from '@/data/apis';

describe('BTC Asset Detail Integration', () => {
  it('should fetch real Bitcoin data with 50 days by default', async () => {
    const result = await getChartData('bitcoin', 50, false);

    expect(result).toBeDefined();
    expect(result.data).toBeDefined();
    expect(result.macdData).toBeDefined();
    expect(result.symbol).toBe('BITCOIN');

    // Should have approximately 50 data points (may be 51 including current day)
    expect(result.data.length).toBeGreaterThanOrEqual(50);
    expect(result.data.length).toBeLessThanOrEqual(52);
    
    // Should have MACD data
    expect(result.macdData.length).toBeGreaterThan(0);
    
    // Should indicate API source if successful, or mock if fallback
    expect(['api', 'mock']).toContain(result.source);
    
    // Data should be properly formatted
    result.data.forEach(point => {
      expect(point).toHaveProperty('time');
      expect(point).toHaveProperty('open');
      expect(point).toHaveProperty('high');
      expect(point).toHaveProperty('low');
      expect(point).toHaveProperty('close');
      expect(typeof point.open).toBe('number');
      expect(typeof point.high).toBe('number');
      expect(typeof point.low).toBe('number');
      expect(typeof point.close).toBe('number');
    });
  });

  it('should use correct symbol mapping for BTC', () => {
    const SYMBOL_TO_COINGECKO_ID = {
      BTC: "bitcoin",
      ETH: "ethereum",
    };
    
    expect(SYMBOL_TO_COINGECKO_ID.BTC).toBe('bitcoin');
    expect(SYMBOL_TO_COINGECKO_ID.ETH).toBe('ethereum');
  });
});
